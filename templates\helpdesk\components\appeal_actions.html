{% if ticket.status in ['new', 'open'] and ticket.assigned_to == session.user_id %}
<!-- Active Appeal - Show Action Buttons -->
<div class="rounded-3">
    <div class="d-flex align-items-start">
        <div>
            <h6 class="fw-bold mb-1">
                {% if ticket.appeal_type == 'hidden_journey' %}
                Journey Appeal
                {% elif ticket.appeal_type == 'sharing_block' %}
                Blocked User Appeal
                {% elif ticket.appeal_type == 'ban' %}
                Ban Appeal
                {% else %}
                Appeal
                {% endif %}
            </h6>
            <p class="mb-0 small">
                {% if ticket.appeal_type == 'hidden_journey' %}
                This appeal is assigned to you. You can approve to unhide the journey or reject with a reason.
                {% elif ticket.appeal_type == 'sharing_block' %}
                This appeal is assigned to you. You can approve to unblock the user or reject with a reason.
                {% elif ticket.appeal_type == 'ban' %}
                This appeal is assigned to you. You can approve to unban the user or reject with a reason.
                {% else %}
                This appeal is assigned to you. Review and decide whether to approve or reject it.
                {% endif %}
            </p>
        </div>
    </div>
</div>

<div class="d-flex gap-2">
    <button type="button" class="btn btn-success btn-sm rounded-pill appeal-action-btn py-2 px-3"
        data-action="approve" data-appeal-id="{{ ticket.id }}">
        <i class="bi bi-check-lg me-1"></i>Approve Appeal
    </button>
    <button type="button" class="btn btn-danger btn-sm rounded-pill appeal-action-btn py-2 px-3"
        data-action="reject" data-appeal-id="{{ ticket.id }}">
        <i class="bi bi-x-lg me-1"></i>Reject Appeal
    </button>
</div>

{% elif ticket.status == 'approved' %}
<!-- Approved Appeal -->
<div class="alert alert-success rounded-3 mb-0">
    <div class="d-flex align-items-start">
        <i class="bi bi-check-circle-fill fs-5 me-3 mt-1"></i>
        <div>
            <h6 class="fw-bold mb-1">Appeal Approved</h6>
            <p class="mb-0 small">
                {% if ticket.appeal_type == 'hidden_journey' %}
                This journey appeal has been approved and the journey has been made visible again.
                {% elif ticket.appeal_type == 'sharing_block' %}
                This user appeal has been approved and the user has been unblocked from sharing.
                {% elif ticket.appeal_type == 'ban' %}
                This ban appeal has been approved and the user has been unbanned.
                {% else %}
                This appeal has been approved.
                {% endif %}
            </p>
            {% if ticket.admin_response %}
            <div class="mt-2 p-2 bg-white rounded">
                <small class="fw-bold">Staff Response:</small>
                <p class="mb-0 small">{{ ticket.admin_response }}</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

{% elif ticket.status == 'rejected' %}
<!-- Rejected Appeal -->
<div class="alert alert-danger rounded-3 mb-0">
    <div class="d-flex align-items-start">
        <i class="bi bi-x-circle-fill fs-5 me-3 mt-1"></i>
        <div>
            <h6 class="fw-bold mb-1">Appeal Rejected</h6>
            <p class="mb-0 small">
                {% if ticket.appeal_type == 'hidden_journey' %}
                This journey appeal has been reviewed and rejected. The journey remains hidden.
                {% elif ticket.appeal_type == 'sharing_block' %}
                This user appeal has been reviewed and rejected. The user remains blocked from sharing.
                {% elif ticket.appeal_type == 'ban' %}
                This ban appeal has been reviewed and rejected. The user remains banned.
                {% else %}
                This appeal has been reviewed and rejected.
                {% endif %}
            </p>
            {% if ticket.admin_response %}
            <div class="mt-2 p-2 bg-white rounded">
                <small class="fw-bold">Staff Response:</small>
                <p class="mb-0 small">{{ ticket.admin_response }}</p>
            </div>
            {% endif %}
        </div>
    </div>
</div>

{% else %}
<!-- Other statuses or not assigned to current user -->
<div class="alert alert-info rounded-3 mb-0">
    <div class="d-flex align-items-start">
        <i class="bi bi-info-circle-fill fs-5 me-3 mt-1"></i>
        <div>
            <h6 class="fw-bold mb-1">
                {% if ticket.appeal_type == 'hidden_journey' %}
                Journey Appeal
                {% elif ticket.appeal_type == 'sharing_block' %}
                Blocked User Appeal
                {% elif ticket.appeal_type == 'ban' %}
                Ban Appeal
                {% else %}
                Appeal
                {% endif %}
            </h6>
            <p class="mb-0 small">
                {% if ticket.status == 'resolved' %}
                This appeal has been resolved.
                {% elif ticket.assigned_to and ticket.assigned_to != session.user_id %}
                This appeal is assigned to another staff member for review.
                {% else %}
                This appeal is pending review.
                {% endif %}
            </p>
        </div>
    </div>
</div>
{% endif %}
