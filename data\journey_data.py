from typing import Dict, List, Optional, Any
from datetime import date
from utils.db_utils import execute_query
from utils.logger import get_logger

# Initialize logger
logger = get_logger(__name__)

def create_journey(user_id: int, title: str, description: str, start_date: date,
                  visibility: str = 'private', cover_image: Optional[str] = None,
                  no_edits: bool = False) -> int:
    """Create a new journey.

    Args:
        user_id: The ID of the user creating the journey.
        title: The title of the journey.
        description: The description of the journey.
        start_date: The start date of the journey.
        visibility: The visibility of the journey ('private', 'public', 'published').
        cover_image: Optional cover image path.
        no_edits: Whether the journey should be protected from edits by staff.

    Returns:
        int: The ID of the newly created journey.
    """
    logger.info(f"Creating journey '{title}' for user ID: {user_id} with visibility: {visibility}, no_edits: {no_edits}")
    query = """
    INSERT INTO journeys
    (user_id, title, description, start_date, visibility, cover_image, no_edits)
    VALUES (%s, %s, %s, %s, %s, %s, %s)
    """
    journey_id = execute_query(
        query,
        (user_id, title, description, start_date, visibility, cover_image, no_edits)
    )
    logger.info(f"Created journey with ID: {journey_id}")
    return journey_id


def get_journey(journey_id: int) -> Optional[Dict[str, Any]]:
    """Get a journey by ID with primary event image.

    Args:
        journey_id: The ID of the journey to retrieve.

    Returns:
        Dict[str, Any]: Journey data including user info and event image if found, None otherwise.
    """
    import os
    logger.debug(f"Getting journey with ID: {journey_id}")
    query = """
    SELECT j.*, u.username, u.profile_image,
           j.cover_image AS cover_image,
           (SELECT ei.image_filename
            FROM events e
            JOIN event_images ei ON e.id = ei.event_id
            WHERE e.journey_id = j.id AND ei.is_primary = TRUE
            ORDER BY e.created_at ASC
            LIMIT 1) AS event_image
    FROM journeys j
    JOIN users u ON j.user_id = u.id
    WHERE j.id = %s
    """
    result = execute_query(query, (journey_id,), fetch_one=True)
    
    # Filter out event image if it doesn't exist on the file system
    if result and result.get('event_image'):
        file_path = os.path.join('static', 'uploads', 'event_images', result['event_image'])
        if not os.path.exists(file_path):
            logger.warning(f"Event image file not found: {file_path} (removing from journey {journey_id})")
            result['event_image'] = None
    
    logger.debug(f"Journey lookup result: {'Found' if result else 'Not found'}")
    return result

def get_private_journeys(user_id: int) -> List[Dict[str, Any]]:
    """Get all journeys for a user with their first event photo and event count.

    Args:
        user_id: The ID of the user to get journeys for.

    Returns:
        List[Dict[str, Any]]: List of journey records with event images and counts.
    """
    import os
    logger.debug(f"Getting private journeys for user ID: {user_id}")
    query = """
    SELECT j.*, u.username, u.profile_image,
           j.cover_image,
           (SELECT ei.image_filename
            FROM events e
            JOIN event_images ei ON e.id = ei.event_id
            WHERE e.journey_id = j.id AND ei.is_primary = TRUE
            ORDER BY e.created_at ASC LIMIT 1) AS event_image,
           (SELECT COUNT(*) FROM events e WHERE e.journey_id = j.id) AS event_count
    FROM journeys j
    JOIN users u ON j.user_id = u.id
    WHERE j.user_id = %s
    ORDER BY j.start_date DESC
    """
    results = execute_query(query, (user_id,), fetch_all=True)
    
    # Filter out event images that don't exist on the file system
    if results:
        for journey in results:
            if journey.get('event_image'):
                file_path = os.path.join('static', 'uploads', 'event_images', journey['event_image'])
                if not os.path.exists(file_path):
                    logger.warning(f"Event image file not found: {file_path} (removing from journey {journey['id']})")
                    journey['event_image'] = None
    
    logger.debug(f"Found {len(results) if results else 0} private journeys for user ID: {user_id}")
    return results or []

def get_public_journeys(limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """Get all public journeys with their primary event image.

    Args:
        limit: Maximum number of results to return.
        offset: Number of results to skip.

    Returns:
        List[Dict[str, Any]]: List of public journey records.
    """
    import os
    logger.debug(f"Getting public journeys with limit: {limit}, offset: {offset}")
    query = """
    SELECT j.*, u.username, u.first_name, u.last_name, u.profile_image,
           j.cover_image,
           (SELECT ei.image_filename
            FROM events e
            JOIN event_images ei ON e.id = ei.event_id
            WHERE e.journey_id = j.id AND ei.is_primary = TRUE
            ORDER BY e.created_at ASC LIMIT 1) as event_image
    FROM journeys j
    JOIN users u ON j.user_id = u.id
    WHERE (j.visibility = 'public' OR j.visibility = 'published') AND j.is_hidden = FALSE
    ORDER BY j.updated_at DESC
    LIMIT %s OFFSET %s
    """
    results = execute_query(query, (limit, offset), fetch_all=True)
    
    # Filter out event images that don't exist on the file system
    if results:
        for journey in results:
            if journey.get('event_image'):
                file_path = os.path.join('static', 'uploads', 'event_images', journey['event_image'])
                if not os.path.exists(file_path):
                    logger.warning(f"Event image file not found: {file_path} (removing from journey {journey['id']})")
                    journey['event_image'] = None
    
    logger.debug(f"Found {len(results) if results else 0} public journeys")
    return results or []

def get_published_journey(journey_id: int) -> Optional[Dict[str, Any]]:
    """Get all published journeys (visible to non-logged-in users) with their primary event image.

    Args:
        journey_id: The ID of the journey to retrieve.

    Returns:
        Optional[Dict[str, Any]]: Published journey record if found, None otherwise.
    """
    import os
    logger.debug(f"Getting journey with ID: {journey_id}")
    query = """
    SELECT j.*, u.username, u.profile_image,
           (SELECT ei.image_filename
            FROM events e
            JOIN event_images ei ON e.id = ei.event_id
            WHERE e.journey_id = j.id AND ei.is_primary = TRUE
            ORDER BY e.created_at ASC
            LIMIT 1) AS event_image
    FROM journeys j
    JOIN users u ON j.user_id = u.id
    WHERE j.id = %s
    """
    result = execute_query(query, (journey_id,), fetch_one=True)
    
    # Filter out event image if it doesn't exist on the file system
    if result and result.get('event_image'):
        file_path = os.path.join('static', 'uploads', 'event_images', result['event_image'])
        if not os.path.exists(file_path):
            logger.warning(f"Event image file not found: {file_path} (removing from journey {journey_id})")
            result['event_image'] = None
    
    logger.debug(f"Journey lookup result: {'Found' if result else 'Not found'}")
    return result

def get_published_journeys(limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """Get all published journeys (visible to non-logged-in users) with their primary event image.

    Args:
        limit: Maximum number of results to return.
        offset: Number of results to skip.

    Returns:
        List[Dict[str, Any]]: List of published journey records.
    """
    import os
    logger.debug(f"Getting published journeys with limit: {limit}, offset: {offset}")
    query = """
    SELECT j.*, u.username, u.first_name, u.last_name, u.profile_image,
           j.cover_image,
           (SELECT ei.image_filename
            FROM events e
            JOIN event_images ei ON e.id = ei.event_id
            WHERE e.journey_id = j.id AND ei.is_primary = TRUE
            ORDER BY e.created_at ASC LIMIT 1) as event_image
    FROM journeys j
    JOIN users u ON j.user_id = u.id
    WHERE j.visibility = 'published' AND j.is_hidden = FALSE
    ORDER BY j.updated_at DESC
    LIMIT %s OFFSET %s
    """
    results = execute_query(query, (limit, offset), fetch_all=True)
    
    # Filter out event images that don't exist on the file system
    if results:
        for journey in results:
            if journey.get('event_image'):
                file_path = os.path.join('static', 'uploads', 'event_images', journey['event_image'])
                if not os.path.exists(file_path):
                    logger.warning(f"Event image file not found: {file_path} (removing from journey {journey['id']})")
                    journey['event_image'] = None
    
    logger.debug(f"Found {len(results) if results else 0} published journeys")
    return results or []

def get_all_journeys(limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """Get all journeys, ordered by last updated (newest first).

    Args:
        limit: Maximum number of results to return.
        offset: Number of results to skip.

    Returns:
        List[Dict[str, Any]]: List of journey records.
    """
    logger.debug(f"Getting all journeys with limit: {limit}, offset: {offset}")
    query = """
    SELECT j.*, u.username, u.first_name, u.last_name
    FROM journeys j
    JOIN users u ON j.user_id = u.id
    ORDER BY j.updated_at DESC
    LIMIT %s OFFSET %s
    """
    results = execute_query(query, (limit, offset), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} journeys")
    return results or []

def get_public_journey_count() -> int:
    """Fetch the total number of public journeys.

    Returns:
        int: Count of public journeys.
    """
    logger.debug("Counting public journeys")
    query = """
    SELECT COUNT(*) as count
    FROM journeys
    WHERE (visibility = 'public' OR visibility = 'published') AND is_hidden = FALSE
    """
    result = execute_query(query, fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} public journeys")
    return count

def get_published_journey_count() -> int:
    """Fetch the total number of published journeys.

    Returns:
        int: Count of published journeys.
    """
    logger.debug("Counting published journeys")
    query = """
    SELECT COUNT(*) as count
    FROM journeys
    WHERE visibility = 'published' AND is_hidden = FALSE
    """
    result = execute_query(query, fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} published journeys")
    return count

def get_hidden_journeys() -> List[Dict[str, Any]]:
    """Get all hidden journeys, grouped by user.

    Returns:
        List[Dict[str, Any]]: List of hidden journey records.
    """
    logger.debug("Getting all hidden journeys")
    query = """
    SELECT j.*, u.username, u.first_name, u.last_name, u.profile_image
    FROM journeys j
    JOIN users u ON j.user_id = u.id
    WHERE j.is_hidden = TRUE
    ORDER BY j.user_id, j.updated_at DESC
    """
    results = execute_query(query, fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} hidden journeys")
    return results or []


def update_journey(journey_id: int, title: Optional[str] = None,
                  description: Optional[str] = None,
                  start_date: Optional[date] = None,
                  visibility: Optional[str] = None,
                  cover_image: Optional[str] = None,
                  no_edits: Optional[bool] = None) -> int:
    """Update journey details.

    Args:
        journey_id: The ID of the journey to update.
        title: Optional new title for the journey.
        description: Optional new description for the journey.
        start_date: Optional new start date for the journey.
        visibility: Optional new visibility for the journey ('private', 'public', 'published').
        cover_image: Optional new cover image path.
        no_edits: Optional flag to prevent editors from editing the journey.

    Returns:
        int: Number of rows affected by the update operation.
    """
    logger.info(f"Updating journey with ID: {journey_id}")
    updates = []
    params = []

    if title is not None:
        updates.append("title = %s")
        params.append(title)
        logger.debug(f"Updating title to: {title}")

    if description is not None:
        updates.append("description = %s")
        params.append(description)
        logger.debug("Updating description")

    if start_date is not None:
        updates.append("start_date = %s")
        params.append(start_date)
        logger.debug(f"Updating start_date to: {start_date}")

    if visibility is not None:
        updates.append("visibility = %s")
        params.append(visibility)
        logger.debug(f"Updating visibility to: {visibility}")

    if cover_image is not None:
        updates.append("cover_image = %s")
        params.append(cover_image)
        logger.debug(f"Updating cover_image")

    if no_edits is not None:
        updates.append("no_edits = %s")
        params.append(no_edits)
        logger.debug(f"Updating no_edits to: {no_edits}")

    if not updates:
        logger.warning(f"No updates provided for journey ID: {journey_id}")
        return 0

    query = f"""
    UPDATE journeys
    SET {', '.join(updates)}
    WHERE id = %s
    """

    params.append(journey_id)
    rows_affected = execute_query(query, params)
    logger.info(f"Updated journey ID: {journey_id}, rows affected: {rows_affected}")
    return rows_affected


def update_journey_visibility(journey_id: int, visibility: str) -> int:
    """Update journey visibility setting.

    Args:
        journey_id: The ID of the journey to update.
        visibility: New visibility setting ('private', 'public', 'published').

    Returns:
        int: Number of rows affected by the update operation.
    """
    logger.info(f"Updating journey visibility for ID: {journey_id} to {visibility}")

    if visibility not in ['private', 'public', 'published']:
        logger.error(f"Invalid visibility value: {visibility}")
        raise ValueError(f"Invalid visibility value: {visibility}")

    query = """
    UPDATE journeys
    SET visibility = %s
    WHERE id = %s
    """
    rows_affected = execute_query(query, (visibility, journey_id))
    logger.info(f"Updated journey visibility for ID: {journey_id}, rows affected: {rows_affected}")
    return rows_affected


def update_journey_hidden_status(journey_id: int, is_hidden: bool) -> int:
    """Update journey hidden status.

    Args:
        journey_id: The ID of the journey to update.
        is_hidden: Whether the journey should be hidden or not.

    Returns:
        int: Number of rows affected by the update operation.
    """
    logger.info(f"Updating journey hidden status for ID: {journey_id} to {is_hidden}")
    query = """
    UPDATE journeys
    SET is_hidden = %s
    WHERE id = %s
    """
    rows_affected = execute_query(query, (is_hidden, journey_id))
    logger.info(f"Updated journey hidden status for ID: {journey_id}, rows affected: {rows_affected}")
    return rows_affected


def update_journey_no_edits(journey_id: int, no_edits: bool) -> int:
    """Update journey 'no edits' flag.

    Args:
        journey_id: The ID of the journey to update.
        no_edits: Whether the journey should be protected from edits.

    Returns:
        int: Number of rows affected by the update operation.
    """
    logger.info(f"Updating journey 'no edits' flag for ID: {journey_id} to {no_edits}")
    query = """
    UPDATE journeys
    SET no_edits = %s
    WHERE id = %s
    """
    rows_affected = execute_query(query, (no_edits, journey_id))
    logger.info(f"Updated journey 'no edits' flag for ID: {journey_id}, rows affected: {rows_affected}")
    return rows_affected


def update_journey_cover_image(journey_id: int, cover_image: Optional[str]) -> int:
    """Update journey cover image.

    Args:
        journey_id: The ID of the journey to update.
        cover_image: Path to the new cover image or None to remove.

    Returns:
        int: Number of rows affected by the update operation.
    """
    logger.info(f"Updating cover image for journey ID: {journey_id}")
    query = """
    UPDATE journeys
    SET cover_image = %s
    WHERE id = %s
    """
    rows_affected = execute_query(query, (cover_image, journey_id))
    logger.info(f"Updated cover image for journey ID: {journey_id}, rows affected: {rows_affected}")
    return rows_affected


def delete_journey(journey_id: int) -> int:
    """Delete a journey and all its associated events.

    Args:
        journey_id: The ID of the journey to delete.

    Returns:
        int: Number of rows affected by the delete operation.
    """
    logger.info(f"Deleting journey with ID: {journey_id}")
    query = """
    DELETE FROM journeys
    WHERE id = %s
    """
    rows_affected = execute_query(query, (journey_id,))
    logger.info(f"Deleted journey with ID: {journey_id}, rows affected: {rows_affected}")
    return rows_affected

def search_my_journeys(user_id: int, search_term: str, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """Search for the user's own journeys (both public and private).

    Args:
        user_id: The ID of the user to search journeys for.
        search_term: Term to search for in journey titles, descriptions and location names.
        limit: Maximum number of results to return.
        offset: Number of results to skip.

    Returns:
        List[Dict[str, Any]]: List of matching journey records.
    """
    logger.debug(f"Searching journeys for user ID: {user_id} with term: '{search_term}'")
    search_pattern = f"%{search_term}%"

    query = """
    SELECT DISTINCT j.*,
           u.username,
           u.first_name,
           u.last_name,
           j.cover_image,
           (SELECT ei.image_filename
            FROM events e
            JOIN event_images ei ON e.id = ei.event_id
            WHERE e.journey_id = j.id AND ei.is_primary = TRUE
            ORDER BY e.created_at ASC LIMIT 1) AS event_image,
           (SELECT COUNT(*)
            FROM events e
            WHERE e.journey_id = j.id) AS event_count
    FROM journeys j
    JOIN users u ON j.user_id = u.id
    LEFT JOIN events e ON j.id = e.journey_id
    LEFT JOIN locations l ON e.location_id = l.id
    WHERE j.user_id = %s
      AND j.is_hidden = FALSE
      AND (
        j.title LIKE %s OR
        j.description LIKE %s OR
        LOWER(TRIM(l.name)) LIKE LOWER(TRIM(%s))
      )
    ORDER BY j.updated_at DESC
    LIMIT %s OFFSET %s
    """
    results = execute_query(query, (user_id, search_pattern, search_pattern, search_pattern, limit, offset), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} matching journeys for user ID: {user_id}")
    return results or []


def search_journeys(search_term: str, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """Search for public journeys with their primary event image.

    Args:
        search_term: Term to search for in journey titles, descriptions and location names.
        limit: Maximum number of results to return.
        offset: Number of results to skip.

    Returns:
        List[Dict[str, Any]]: List of matching public journey records.
    """
    logger.debug(f"Searching public journeys with term: '{search_term}'")
    search_pattern = f"%{search_term}%"

    query = """
    SELECT DISTINCT j.*, u.username, u.first_name, u.last_name, u.profile_image,
           j.cover_image,
           (SELECT ei.image_filename
            FROM events e
            JOIN event_images ei ON e.id = ei.event_id
            WHERE e.journey_id = j.id AND ei.is_primary = TRUE
            ORDER BY e.created_at ASC LIMIT 1) as event_image
    FROM journeys j
    JOIN users u ON j.user_id = u.id
    LEFT JOIN events e ON j.id = e.journey_id
    LEFT JOIN locations l ON e.location_id = l.id
    WHERE (j.visibility = 'public' OR j.visibility = 'published') AND j.is_hidden = FALSE
      AND (
        j.title LIKE %s OR
        j.description LIKE %s OR
        LOWER(TRIM(l.name)) LIKE LOWER(TRIM(%s))
      )
    ORDER BY j.updated_at DESC
    LIMIT %s OFFSET %s
    """

    results = execute_query(query, (search_pattern, search_pattern, search_pattern, limit, offset), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} matching public journeys")
    return results or []


def search_journeys_by_location(location_id: int, limit: int = 50, offset: int = 0) -> List[Dict[str, Any]]:
    """Search for public journeys containing events at a specific location.

    Args:
        location_id: The ID of the location to search for events at.
        limit: Maximum number of results to return.
        offset: Number of results to skip.

    Returns:
        List[Dict[str, Any]]: List of matching journey records.
    """
    logger.debug(f"Searching journeys for location ID: {location_id}")
    query = """
    SELECT DISTINCT j.*, u.username, u.first_name, u.last_name, j.cover_image
    FROM journeys j
    JOIN events e ON j.id = e.journey_id
    JOIN users u ON j.user_id = u.id
    WHERE (j.visibility = 'public' OR j.visibility = 'published') AND j.is_hidden = FALSE
      AND e.location_id = %s
    ORDER BY j.updated_at DESC
    LIMIT %s OFFSET %s
    """
    results = execute_query(query, (location_id, limit, offset), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} journeys for location ID: {location_id}")
    return results or []


def count_journey_events(journey_id: int) -> int:
    """Count the number of events in a journey.

    Args:
        journey_id: The ID of the journey to count events for.

    Returns:
        int: Count of events in the journey.
    """
    logger.debug(f"Counting events for journey ID: {journey_id}")
    query = """
    SELECT COUNT(*) as count
    FROM events
    WHERE journey_id = %s
    """
    result = execute_query(query, (journey_id,), fetch_one=True)
    count = result['count'] if result else 0
    logger.debug(f"Found {count} events for journey ID: {journey_id}")
    return count


def check_journey_exists(journey_id: int) -> bool:
    """Check if a journey exists.

    Args:
        journey_id: The ID of the journey to check.

    Returns:
        bool: True if the journey exists, False otherwise.
    """
    logger.debug(f"Checking if journey ID: {journey_id} exists")
    query = """
    SELECT COUNT(*) as count
    FROM journeys
    WHERE id = %s
    """
    result = execute_query(query, (journey_id,), fetch_one=True)
    exists = result['count'] > 0 if result else False
    logger.debug(f"Journey ID: {journey_id} exists: {exists}")
    return exists


def check_journey_owner(journey_id: int, user_id: int) -> bool:
    """Check if a user is the owner of a journey.

    Args:
        journey_id: The ID of the journey to check.
        user_id: The ID of the user to check against.

    Returns:
        bool: True if the user is the owner of the journey, False otherwise.
    """
    logger.debug(f"Checking if user ID: {user_id} is the owner of journey ID: {journey_id}")
    query = """
    SELECT COUNT(*) as count
    FROM journeys
    WHERE id = %s AND user_id = %s
    """
    result = execute_query(query, (journey_id, user_id), fetch_one=True)
    is_owner = result['count'] > 0 if result else False
    logger.debug(f"User ID: {user_id} is owner of journey ID: {journey_id}: {is_owner}")
    return is_owner

def check_journey_no_edits(journey_id: int) -> bool:
    """Check if a journey has the 'no edits' flag set.

    Args:
        journey_id: The ID of the journey to check.

    Returns:
        bool: True if the journey has 'no edits' flag set, False otherwise.
    """
    logger.debug(f"Checking if journey ID: {journey_id} has 'no edits' flag set")
    query = """
    SELECT no_edits
    FROM journeys
    WHERE id = %s
    """
    result = execute_query(query, (journey_id,), fetch_one=True)
    no_edits = result['no_edits'] if result else False
    logger.debug(f"Journey ID: {journey_id} has 'no edits' flag set: {no_edits}")
    return no_edits

# def create_journey_path(journey_id: int, start_event_id: int, end_event_id: int,
#                        path_type: str = 'custom', path_color: str = '#3388ff',
#                        path_thickness: int = 3, distance_km: Optional[float] = None) -> int:
#     """Create a new journey path between two events.

#     Args:
#         journey_id: The ID of the journey.
#         start_event_id: The ID of the starting event.
#         end_event_id: The ID of the ending event.
#         path_type: The type of path ('air', 'road', 'rail', 'sea', 'custom').
#         path_color: The color of the path (hex code).
#         path_thickness: The thickness of the path.
#         distance_km: Optional distance in kilometers.

#     Returns:
#         int: The ID of the newly created path.
#     """
#     logger.info(f"Creating journey path for journey ID: {journey_id} from event ID: {start_event_id} to event ID: {end_event_id}")
#     query = """
#     INSERT INTO journey_paths
#     (journey_id, start_event_id, end_event_id, path_type, path_color, path_thickness, distance_km)
#     VALUES (%s, %s, %s, %s, %s, %s, %s)
#     """
#     path_id = execute_query(
#         query,
#         (journey_id, start_event_id, end_event_id, path_type, path_color, path_thickness, distance_km)
#     )
#     logger.info(f"Created journey path with ID: {path_id}")
#     return path_id

# def get_journey_paths(journey_id: int) -> List[Dict[str, Any]]:
#     """Get all paths for a journey.

#     Args:
#         journey_id: The ID of the journey to get paths for.

#     Returns:
#         List[Dict[str, Any]]: List of path records.
#     """
#     logger.debug(f"Getting paths for journey ID: {journey_id}")
#     query = """
#     SELECT jp.*,
#            se.title as start_event_title,
#            ee.title as end_event_title
#     FROM journey_paths jp
#     JOIN events se ON jp.start_event_id = se.id
#     JOIN events ee ON jp.end_event_id = ee.id
#     WHERE jp.journey_id = %s
#     ORDER BY jp.id
#     """
#     results = execute_query(query, (journey_id,), fetch_all=True)
#     logger.debug(f"Found {len(results) if results else 0} paths for journey ID: {journey_id}")
#     return results or []

def get_user_public_journeys(user_id: int, limit: int = 10, offset: int = 0) -> List[Dict[str, Any]]:
    """Get all public or published journeys for a specific user."""
    logger.debug(f"Getting public journeys for user ID: {user_id} with limit: {limit}, offset: {offset}")
    query = """
    SELECT j.*, u.username, u.first_name, u.last_name, u.profile_image,
           j.cover_image,
           (SELECT ei.image_filename
            FROM events e
            JOIN event_images ei ON e.id = ei.event_id
            WHERE e.journey_id = j.id AND ei.is_primary = TRUE
            ORDER BY e.created_at ASC LIMIT 1) as event_image
    FROM journeys j
    JOIN users u ON j.user_id = u.id
    WHERE (j.visibility = 'public' OR j.visibility = 'published')
      AND j.is_hidden = FALSE
      AND j.user_id = %s
    ORDER BY j.updated_at DESC
    LIMIT %s OFFSET %s
    """
    results = execute_query(query, (user_id, limit, offset), fetch_all=True)
    logger.debug(f"Found {len(results) if results else 0} public journeys for user ID: {user_id}")
    return results or []