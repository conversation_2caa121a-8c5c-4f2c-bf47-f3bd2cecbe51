<form id="takeTicketForm" method="POST" action="{{ url_for('helpdesk.take_ticket') }}">
    <input type="hidden" name="ticket_id" value="{{ ticket.id }}">
    <!-- Preserve back parameter for navigation -->
    {% if request.args.get('back') %}
    <input type="hidden" name="back" value="{{ request.args.get('back') }}">
    {% endif %}
    
    {% if ticket.assigned_to %}
    <!-- Warning for taking over an assigned ticket -->
    <div class="alert alert-warning rounded-3 mb-3">
        <div class="d-flex align-items-start">
            <i class="bi bi-exclamation-triangle-fill fs-5 me-3 mt-1"></i>
            <div>
                <h6 class="fw-bold mb-1">Take Over Ticket</h6>
                <p class="mb-0 small">This ticket is currently assigned to <strong>{{ ticket.get('assigned_username', ticket.staff_name) }}</strong>. Taking over will reassign it to you and notify the previous assignee.</p>
            </div>
        </div>
    </div>
    <p class="text-warning"><strong>⚠️ Are you sure you want to take over this ticket from {{ ticket.get('assigned_username', ticket.staff_name) }}?</strong></p>
    {% else %}
    <!-- Normal take ticket message -->
    <div class="alert alert-success rounded-3 mb-3">
        <div class="d-flex align-items-start">
            <i class="bi bi-person-plus-fill fs-5 me-3 mt-1"></i>
            <div>
                <h6 class="fw-bold mb-1">Take Ticket</h6>
                <p class="mb-0 small">This will assign the ticket to you and change its status to "open".</p>
            </div>
        </div>
    </div>
    <p>Are you sure you want to take this ticket?</p>
    {% endif %}
</form>
