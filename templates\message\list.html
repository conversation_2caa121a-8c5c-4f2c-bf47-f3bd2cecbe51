{% extends "base.html" %}

{% block title %}Messages - Footprints{% endblock %}

{% block content %}
<div class="container">
  <div class="row align-items-center mb-4">
    <div class="col-md-6 mb-3 mb-md-0 d-flex align-items-center">
      <h1 class="display-6 fw-bold mb-0">
        <span class="position-relative">
          Messages
          <span class="position-absolute start-0 bottom-0 bg-primary opacity-25 rounded"
            style="height: 6px; width: 60%;"></span>
        </span>
      </h1>
    </div>
    <div class="col-md-6">
      <div class="d-flex justify-content-md-end">
        <button class="btn btn-primary rounded-pill px-4 py-2 d-flex align-items-center" id="newMessageBtn">
          <i class="bi bi-plus-lg me-2"></i> New Message
        </button>
      </div>
    </div>
  </div>
  <div class="row g-4 align-items-stretch" id="messageContainer">
    <!-- Message List Panel -->
    <div class="col-md-4 d-flex flex-column">
      <div class="card shadow-sm border-0 rounded-3" style="height:  calc(100vh - 300px);">
        <div class="card-body d-flex flex-column p-0" style="height: 100%;">
          <div class="p-3" style="height: 100%; display: flex; flex-direction: column;">
            <div style="flex: 0 0 auto;">
              <div class="input-group mb-2">
                <span class="input-group-text bg-light border-end-0">
                  <i class="bi bi-search"></i>
                </span>
                <input type="text" class="form-control bg-light border-start-0" placeholder="Search messages..."
                  id="searchMessages">
              </div>
            </div>
            <div id="conversationsList" style="flex: 1 1 0; overflow-y: auto; height: 100%;">
              {% if conversations %}
              {% for conversation in conversations %}
              <a href="javascript:void(0)"
                class="list-group-item list-group-item-action py-3 px-3 border-0 conversation-item"
                data-user-id="{{ conversation.other_user_id }}">
                <div class="d-flex align-items-center">
                  <div class="position-relative me-3">
                    {% if conversation.other_user.profile_image %}
                    <img
                      src="{{ url_for('static', filename='uploads/profile_images/' + conversation.other_user.profile_image) }}"
                      class="rounded-circle" alt="{{ conversation.other_user.username }}" width="48" height="48"
                      onerror="this.onerror=null; this.src='/static/uploads/profile_images/profile_placeholder.png';">
                    {% else %}
                    <img src="{{ url_for('static', filename='uploads/profile_images/profile_placeholder.png') }}"
                      class="rounded-circle" alt="Profile placeholder" width="48" height="48">
                    {% endif %}
                  </div>
                  <div class="flex-grow-1 overflow-hidden">
                    <div class="d-flex justify-content-between align-items-center">
                      <h6 class="mb-0 {% if conversation.unread_count > 0 %}fw-bold{% endif %} text-truncate">
                        {{ conversation.other_user.username }}
                      </h6>
                      <small
                        class="{% if conversation.unread_count > 0 %}text-primary{% else %}text-muted{% endif %} ms-2">
                        {{ conversation.last_message_time | timeago }}
                      </small>
                    </div>
                    <p class="mb-0 small text-truncate">{{ conversation.last_message }}</p>
                  </div>
                  {% if conversation.unread_count > 0 %}
                  <span class="badge bg-primary rounded-pill ms-2">{{ conversation.unread_count }}</span>
                  {% endif %}
                </div>
              </a>
              {% endfor %}
              {% else %}
              <div
                style="height:100%; display:flex; flex-direction:column; justify-content:center; align-items:center;">
                <p class="text-muted mb-0">No conversations yet.</p>
              </div>
              {% endif %}
            </div>
          </div>

        </div>
      </div>
    </div>

    <!-- Instructions Panel -->
    <div class="col-md-8 d-flex flex-column" style="height: calc(100vh - 300px);">
      <div id="conversationPanelWrapper" class="h-100">
        <div id="conversationPanel" class="h-100">
          {% if not conversations or conversations|length == 0 %}
          <div class="card shadow-sm border-0 rounded-3 h-100 d-flex flex-column empty-state-card">
            <div class="card-body d-flex flex-column p-0 h-100">
              <div class="d-flex flex-column align-items-center justify-content-center h-100">
                <div class="text-center p-4">
                  <i class="bi bi-chat-dots text-muted" style="font-size: 4rem;"></i>
                  <h3 class="mt-3 mb-3">Your Messages</h3>
                  <p class="text-muted">Click New Message to start!</p>
                </div>
              </div>
            </div>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  /* Custom scrollbar for better UX */
  ::-webkit-scrollbar {
    width: 6px;
  }

  ::-webkit-scrollbar-track {
    background: #f1f1f1;
  }

  ::-webkit-scrollbar-thumb {
    background: #d1d1d1;
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #b1b1b1;
  }

  /* User search results dropdown fixed height */
  #userSearchResults {
    max-height: 180px;
    overflow-y: auto;
    border: 1px solid #eee;
    border-radius: 8px;
  }

  #userSearchResults .list-group-item.active {
    background: #5a6ffb !important;
    color: #fff !important;
  }

  /* 선택된 대화 강조 (항상 유지) */
  #conversationsList .conversation-item.active {
    background: #5a6ffb !important;
    color: #fff !important;
    border-radius: 0 !important;
  }

  #conversationsList .conversation-item.active h6,
  #conversationsList .conversation-item.active p,
  #conversationsList .conversation-item.active small {
    color: #fff !important;
  }

  #conversationsList .conversation-item.active .badge {
    background: #fff !important;
    color: #5a6ffb !important;
  }

  /* Custom dropdown for user search */
  .custom-dropdown {
    position: relative;
    background: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
    max-height: 220px;
    overflow-y: auto;
    margin-top: -8px;
    z-index: 1051;
  }

  .custom-dropdown .dropdown-item,
  .custom-dropdown .list-group-item {
    border: none;
    border-bottom: 1px solid #f1f1f1;
    background: #fff;
    padding: 14px 18px;
    font-size: 1.05rem;
    cursor: pointer;
    transition: background 0.15s;
  }

  .custom-dropdown .dropdown-item:last-child,
  .custom-dropdown .list-group-item:last-child {
    border-bottom: none;
  }

  .custom-dropdown .dropdown-item.active,
  .custom-dropdown .list-group-item.active,
  .custom-dropdown .dropdown-item:hover,
  .custom-dropdown .list-group-item:hover {
    background: #f5f7ff;
    color: #2d3748;
  }

  .custom-dropdown .dropdown-item.selected,
  .custom-dropdown .list-group-item.selected {
    background: #5a6ffb;
    color: #fff;
  }

  .empty-state-card {
    height: 100%;
    min-height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
  }
</style>
{% endblock %}

{% block scripts %}
<script>
  var isPremium = "{{ premium_access|default(false)|string|lower }}";

  document.addEventListener('DOMContentLoaded', function () {
    // DOM Elements
    const searchMessages = document.getElementById('searchMessages');
    const newMessageBtn = document.getElementById('newMessageBtn');
    const newMessageBtn2 = document.getElementById('newMessageBtn2');
    const conversationPanel = document.getElementById('conversationPanel');

    function showLoadingIndicator() {
      const messagesContainer = document.getElementById('messages-container');
      if (messagesContainer) {
        messagesContainer.innerHTML = `
          <div class="d-flex justify-content-center align-items-center" style="height: 400px;">
            <div class="spinner-border text-primary mb-3" role="status" style="width: 2.5rem; height: 2.5rem;"></div>
            <div class="fw-bold fs-6 ms-3">LOADING...</div>
          </div>
        `;
      }
    }
    function setupDeleteButtons() {
      document.querySelectorAll('.delete-message-btn').forEach(btn => {
        btn.onclick = function (e) {
          e.preventDefault();
          const messageId = btn.getAttribute('data-message-id');
          if (typeof showModal === 'function') {
            showModal(
              'Delete Message',
              'Are you sure you want to delete this message?',
              {
                actionText: 'Delete',
                onAction: function () {
                  fetch(`/messages/delete/${messageId}`, {
                    method: 'POST',
                    headers: { 'X-Requested-With': 'XMLHttpRequest' }
                  })
                    .then(res => res.json())
                    .then(data => {
                      if (data.success) {
                        const msgEl = document.querySelector(`[data-message-id="${messageId}"]`);
                        if (msgEl) msgEl.remove();
                        if (typeof showFlashMessage === 'function') showFlashMessage('Message deleted.', 'success');
                      } else {
                        if (typeof showFlashMessage === 'function') showFlashMessage(data.message || 'Failed to delete message', 'danger');
                      }
                    });
                  return true;
                }
              }
            );
          }
        };
      });
    }

    function loadConversationPartial(userId) {
      showLoadingIndicator();
      fetch(`/messages/conversation_partial/${userId}`)
        .then(response => response.text())
        .then(html => {
          conversationPanel.innerHTML = html;
          setupDeleteButtons();

          const refreshBtn = document.getElementById('refresh-messages');
          if (refreshBtn) {
            refreshBtn.addEventListener('click', function (e) {
              e.preventDefault();
              loadConversationPartial(userId);
            });
          }

          const messageForm = document.getElementById('message-form');
          if (messageForm) {
            messageForm.addEventListener('submit', function (e) {
              e.preventDefault();
              const formData = new FormData(messageForm);
              const content = formData.get('content').trim();
              if (!content) {
                messageForm.classList.add('was-validated');
                return;
              }
              const messagesContainer = document.getElementById('messages-container');
              if (messagesContainer) {
                messagesContainer.innerHTML = `
                  <div class="d-flex justify-content-center align-items-center" style="height: 200px;">
                    <div class="spinner-border text-primary mb-3" role="status" style="width: 2.5rem; height: 2.5rem;"></div>
                    <div class="fw-bold fs-6 ms-3">LOADING...</div>
                  </div>
                `;
              }
              fetch('/messages/send', {
                method: 'POST',
                headers: { 'X-Requested-With': 'XMLHttpRequest' },
                body: new URLSearchParams(formData)
              })
                .then(res => res.json())
                .then(data => {
                  if (data.success) {
                    loadConversationPartial(userId);
                  } else {
                    if (typeof showFlashMessage === 'function') {
                      showFlashMessage(data.message || 'Failed to send message', 'danger');
                    }
                    loadConversationPartial(userId);
                  }
                })
                .catch(() => {
                  if (typeof showFlashMessage === 'function') {
                    showFlashMessage('Failed to send message', 'danger');
                  }
                  loadConversationPartial(userId);
                });
            });
          }

          const messagesContainer = document.getElementById('messages-container');
          if (messagesContainer) {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
          }
        });
    }

    function openComposeModal(preselectedUser = null) {
      let selectedUser = preselectedUser;
      let messageValue = '';
      let userSearchDropdown = null;

      const modalContent = `
        <div class="compose-message-container">
          <div class="recipient-section mb-3">
            <div id="searchOrSelectedUser">
              ${preselectedUser ? 
                `<div class="selected-recipient d-flex align-items-center justify-content-between p-2 bg-light rounded">
                   <div class="d-flex align-items-center">
                     <img src="${preselectedUser.profile_image ? `/static/uploads/profile_images/${preselectedUser.profile_image}` : '/static/uploads/profile_images/profile_placeholder.png'}" 
                          class="rounded-circle me-2" 
                          alt="${preselectedUser.username}" 
                          width="40" height="40"
                          onerror="this.src='/static/uploads/profile_images/profile_placeholder.png'">
                     <span class="fw-bold">${preselectedUser.username}</span>
                   </div>
                   <button type="button" class="btn btn-link text-muted p-0 change-recipient" title="Change recipient">
                     <i class="bi bi-pencil"></i>
                   </button>
                 </div>`
                :
                `<div class="search-container">
                   <label for="composeUserSearch" class="form-label">To:</label>
                   <input type="text" class="form-control" id="composeUserSearch" placeholder="Search for a user..." autocomplete="off">
                 </div>`
              }
            </div>
          </div>
          <div class="message-section mb-3">
            <label for="composeMessage" class="form-label">Your Message:</label>
            <textarea id="composeMessage" 
                      class="form-control" 
                      placeholder="Type your message here..." 
                      required 
                      rows="4" 
                      style="resize: none;" 
                      ${!preselectedUser ? 'disabled' : ''}></textarea>
          </div>
        </div>
      `;

      const { modalInstance, modalActionBtn } = showModal('New Message', modalContent, {
        actionText: 'Send',
        onAction: () => {
          if (!selectedUser || !messageValue.trim()) {
            if (!selectedUser) {
              document.getElementById('composeUserSearch')?.focus();
            } else {
              document.getElementById('composeMessage').focus();
            }
            return false;
          }
          modalActionBtn.disabled = true;
          fetch('/messages/send', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
              'X-Requested-With': 'XMLHttpRequest'
            },
            body: new URLSearchParams({
              'recipient_id': selectedUser.id,
              'content': messageValue
            })
          })
            .then(res => res.json())
            .then(data => {
              if (data.success) {
                setTimeout(() => {
                  modalInstance.hide();
                  window.location.reload();
                }, 300);
              } else {
                modalActionBtn.disabled = false;
                showFlashMessage(data.message || 'Failed to send message', 'danger');
              }
            })
            .catch(() => {
              modalActionBtn.disabled = false;
              showFlashMessage('Failed to send message', 'danger');
            });
          return false;
        }
      });

      function setupUserSearch() {
        const userSearch = document.getElementById('composeUserSearch');
        if (!userSearch) return;

        function showSelectedUser(user) {
          selectedUser = user;
          const searchContainer = document.getElementById('searchOrSelectedUser');
          
          const selectedUserDisplay = document.createElement('div');
          selectedUserDisplay.className = 'selected-recipient d-flex align-items-center justify-content-between p-2 bg-light rounded';
          selectedUserDisplay.innerHTML = `
            <div class="d-flex align-items-center">
              <img src="${user.profile_image ? `/static/uploads/profile_images/${user.profile_image}` : '/static/uploads/profile_images/profile_placeholder.png'}" 
                   class="rounded-circle me-2" 
                   alt="${user.username}" 
                   width="40" height="40"
                   onerror="this.src='/static/uploads/profile_images/profile_placeholder.png'">
              <span class="fw-bold">${user.username}</span>
            </div>
            <button type="button" class="btn btn-link text-muted p-0 change-recipient" title="Change recipient">
              <i class="bi bi-pencil"></i>
            </button>
          `;
          
          searchContainer.innerHTML = '';
          searchContainer.appendChild(selectedUserDisplay);
          
          const messageInput = document.getElementById('composeMessage');
          messageInput.disabled = false;
          messageInput.focus();
          updateSendButtonState();
          
          selectedUserDisplay.querySelector('.change-recipient').addEventListener('click', () => {
            searchContainer.innerHTML = `
              <div class="search-container">
                <label for="composeUserSearch" class="form-label">To:</label>
                <input type="text" class="form-control" id="composeUserSearch" placeholder="Search for a user..." autocomplete="off">
              </div>
            `;
            selectedUser = null;
            messageInput.disabled = true;
            updateSendButtonState();
            setupUserSearch();
            document.getElementById('composeUserSearch').focus();
          });
        }

        function showUserSearchDropdown(users, anchorInput) {
          if (userSearchDropdown) userSearchDropdown.remove();
          const rect = anchorInput.getBoundingClientRect();
          userSearchDropdown = document.createElement('div');
          userSearchDropdown.className = 'custom-dropdown list-group';
          userSearchDropdown.style.position = 'absolute';
          userSearchDropdown.style.left = '0';
          userSearchDropdown.style.right = '0';
          userSearchDropdown.style.top = '100%';
          userSearchDropdown.style.marginTop = '4px';
          
          if (users.length === 0) {
            userSearchDropdown.innerHTML = '<div class="list-group-item text-muted">No users found.</div>';
          } else {
            users.forEach(user => {
              const userEl = document.createElement('div');
              userEl.className = 'list-group-item dropdown-item';
              userEl.innerHTML = `
                <div class="d-flex align-items-center">
                  <img src="/static/uploads/profile_images/${user.profile_image ? user.profile_image : 'profile_placeholder.png'}"
                       class="rounded-circle me-2"
                       alt="${user.username}"
                       width="32" height="32"
                       onerror="this.onerror=null; this.src='/static/uploads/profile_images/profile_placeholder.png';">
                  <div>${user.username}</div>
                </div>
              `;
              userEl.onclick = () => {
                showSelectedUser(user);
                hideUserSearchDropdown();
              };
              userSearchDropdown.appendChild(userEl);
            });
          }
          anchorInput.parentElement.style.position = 'relative';
          anchorInput.parentElement.appendChild(userSearchDropdown);
        }

        function hideUserSearchDropdown() {
          if (userSearchDropdown) {
            userSearchDropdown.remove();
            userSearchDropdown = null;
          }
        }

        userSearch.addEventListener('input', function () {
          const term = this.value.trim();
          if (term.length < 2) {
            hideUserSearchDropdown();
            if (term.length > 0) {
              showUserSearchDropdown([], userSearch);
            }
            return;
          }
          fetch(`/api/users/search?q=${encodeURIComponent(term)}`)
            .then(res => res.json())
            .then(users => showUserSearchDropdown(users, userSearch));
        });

        userSearch.addEventListener('blur', () => setTimeout(hideUserSearchDropdown, 200));
      }

      const messageInput = document.getElementById('composeMessage');
      messageInput.addEventListener('input', function () {
        messageValue = this.value;
        updateSendButtonState();
      });

      function updateSendButtonState() {
        const hasUser = !!selectedUser;
        const hasMessage = messageInput.value.trim().length > 0;
        modalActionBtn.disabled = !(hasUser && hasMessage);
      }

      modalActionBtn.disabled = true;
      modalInstance._element.addEventListener('hidden.bs.modal', () => {
        if (userSearchDropdown) userSearchDropdown.remove();
      });

      if (!preselectedUser) {
        setupUserSearch();
      }
    }

    // Set up event listeners
    window.setupEventListeners = function () {
      // Search messages
      if (searchMessages) {
        searchMessages.addEventListener('input', function () {
          filterConversations(this.value);
        });
      }
      // Conversation item click
      document.querySelectorAll('.conversation-item').forEach(item => {
        item.addEventListener('click', function (e) {
          e.preventDefault();
          document.querySelectorAll('.conversation-item').forEach(i => i.classList.remove('active'));
          this.classList.add('active');
          const userId = this.getAttribute('data-user-id');
          loadConversationPartial(userId);

          const badge = this.querySelector('.badge.bg-danger');
          if (badge) {
            badge.remove();
          }

          fetch('/messages/mark-read', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
              'X-Requested-With': 'XMLHttpRequest'
            },
            body: new URLSearchParams({ other_user_id: userId })
          });
        });
      });
    }

    // Filter conversations by search term
    function filterConversations(term) {
      term = term.toLowerCase();
      let visibleCount = 0;
      document.querySelectorAll('.conversation-item').forEach(item => {
        const username = item.querySelector('h6').textContent.toLowerCase();
        const lastMessage = item.querySelector('p').textContent.toLowerCase();
        if (username.includes(term) || lastMessage.includes(term)) {
          item.style.display = '';
          visibleCount++;
        } else {
          item.style.display = 'none';
        }
      });
      // Show/hide pagination based on search
      const paginationContainer = document.querySelector('.pagination')?.closest('.p-3');
      if (paginationContainer) {
        if (term.length > 0) {
          paginationContainer.style.display = 'none';
        } else {
          paginationContainer.style.display = 'block';
        }
      }
      // Show no results message if needed
      const conversationsList = document.getElementById('conversationsList');
      let noResultsMsg = conversationsList.querySelector('.no-search-results');
      if (term.length > 0 && visibleCount === 0) {
        if (!noResultsMsg) {
          noResultsMsg = document.createElement('div');
          noResultsMsg.className = 'text-center py-5 no-search-results';
          noResultsMsg.innerHTML = '<p class="text-muted mb-0">No conversations found matching your search.</p>';
          conversationsList.appendChild(noResultsMsg);
        }
        noResultsMsg.style.display = 'block';
      } else if (noResultsMsg) {
        noResultsMsg.style.display = 'none';
      }
    }

    // Initialize
    window.setupEventListeners();
    const firstConversation = document.querySelector('.conversation-item');
    if (firstConversation) {
      document.querySelectorAll('.conversation-item').forEach(i => i.classList.remove('active'));
      firstConversation.classList.add('active');
      const userId = firstConversation.getAttribute('data-user-id');
      loadConversationPartial(userId);
    }

    if (typeof newMessageBtn !== 'undefined' && newMessageBtn) newMessageBtn.addEventListener('click', function () {
      if (isPremium !== "true") {
        window.showPremiumModal();
        return;
      }
      window.openComposeModal();
    });
    if (typeof newMessageBtn2 !== 'undefined' && newMessageBtn2) newMessageBtn2.addEventListener('click', function () {
      if (isPremium !== "true") {
        window.showPremiumModal();
        return;
      }
      window.openComposeModal();
    });

    window.showPremiumModal = function () {
      showModal(
        'Premium Required',
        `<div class="text-center">
          <i class="bi bi-trophy text-warning" style="font-size: 2.5rem;"></i>
          <h4 class="mt-3">Premium Feature</h4>
          <p>You need a premium subscription to compose new messages.<br>Upgrade now to unlock messaging features!</p>
          <a href="/account/profile?active_tab=subscription" class="btn btn-primary rounded-pill px-4 py-2 mt-2">
            <i class="bi bi-star"></i> Upgrade to Premium
          </a>
        </div>`
      );
    };

    window.openComposeModal = openComposeModal;

    const urlParams = new URLSearchParams(window.location.search);
    const userIdToSelect = urlParams.get('user');
    if (userIdToSelect) {
      const targetItem = document.querySelector('.conversation-item[data-user-id="' + userIdToSelect + '"]');
      if (targetItem) {
        targetItem.click();
      } else {
        fetch(`/api/users/${userIdToSelect}`)
          .then(res => res.json())
          .then(user => {
            if (user && user.id) {
              window.openComposeModal(user);
            }
          });
      }
    }
  });
</script>
<style>
  @keyframes spin {
    from {
      transform: rotate(0deg);
    }

    to {
      transform: rotate(360deg);
    }
  }

  /* New styles for enhanced messaging UI */
  .compose-message-container {
    max-width: 100%;
    width: 100%;
  }

  .selected-recipient {
    transition: all 0.2s ease;
    border: 1px solid #dee2e6;
  }

  .selected-recipient:hover {
    background-color: #f8f9fa !important;
  }

  .change-recipient {
    opacity: 0.5;
    transition: opacity 0.2s ease;
  }

  .selected-recipient:hover .change-recipient {
    opacity: 1;
  }

  .search-container {
    position: relative;
  }

  .custom-dropdown {
    position: absolute;
    left: 0;
    right: 0;
    top: 100%;
    margin-top: 4px;
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    box-shadow: 0 4px 16px rgba(0,0,0,0.08);
    max-height: 220px;
    overflow-y: auto;
    z-index: 1061;
  }

  @media (max-width: 576px) {
    .selected-recipient {
      flex-direction: column;
      align-items: flex-start !important;
      gap: 0.5rem;
      padding: 1rem !important;
    }
    
    .selected-recipient .change-recipient {
      align-self: flex-end;
      margin-top: -2rem;
    }

    .custom-dropdown {
      max-height: 50vh;
    }
  }

  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(-10px); }
    to { opacity: 1; transform: translateY(0); }
  }

  .selected-recipient {
    animation: fadeIn 0.3s ease;
  }
</style>
{% endblock %}