<form id="assignTicketForm" method="POST" action="{{ url_for('helpdesk.assign_ticket') }}">
    <input type="hidden" name="ticket_id" value="{{ ticket.id }}">
    <!-- Preserve back parameter for navigation -->
    {% if request.args.get('back') %}
    <input type="hidden" name="back" value="{{ request.args.get('back') }}">
    {% endif %}
    <div class="mb-3">
        <label for="staff_id" class="form-label">Assign to Staff Member</label>
        <select class="form-select" name="staff_id" required style="max-width: 500px;">
            <option value="" disabled {% if not ticket.assigned_to %}selected{% endif %}>Select staff member</option>
            {% for staff in staff_list %}
            {# Only show staff members (editor, support_tech, admin), exclude current user, and exclude currently assigned staff #}
            {% if staff.role in ['editor', 'support_tech', 'admin'] and staff.id != session.user_id and staff.id != ticket.assigned_to %}
            <option value="{{staff.id}}">
                <span class="badge {{ get_role_badge_class(staff.role) }}">
                    <i class="{{ get_role_icon(staff.role) }}"></i>
                </span>
                {{staff.username}} ({{staff.role|title}})
            </option>
            {% endif %}
            {% endfor %}
        </select>
        <div class="form-text">
            <i class="bi bi-info-circle me-1"></i>
            {% if ticket.assigned_to %}
            Reassigning this ticket will notify both the user and the new assignee. Use "Take Ticket" to assign to yourself.
            {% else %}
            Only staff members can be assigned tickets. Use "Take Ticket" to assign to yourself.
            {% endif %}
        </div>
    </div>
</form>
