{% if can_manage_content() and ticket.status not in ['resolved', 'approved', 'rejected'] %}
    {% if ticket.user_id != g.current_user['id'] and ticket.assigned_to != session.user_id %}
    <li>
        <a class="dropdown-item modal-trigger" href="#" data-action="take" data-ticket-id="{{ ticket.id }}">
            <i class="bi bi-person-plus-fill me-2 text-success"></i>{% if ticket.assigned_to %}Take Over Ticket{% else %}Take Ticket{% endif %}
        </a>
    </li>
    <li>
        <hr class="dropdown-divider">
    </li>
    {% endif %}
    <li>
        <a class="dropdown-item modal-trigger" href="#" data-action="assign" data-ticket-id="{{ ticket.id }}">
            <i class="bi bi-person-plus me-2 text-dark"></i>{% if ticket.assigned_to %}Reassign Ticket{% else %}Assign Ticket{% endif %}
        </a>
    </li>
    {% if ticket.assigned_to and ticket.assigned_to == session.user_id and ticket.request_type != 'appeal' %}
    <li>
        <a class="dropdown-item modal-trigger" href="#" data-action="change" data-ticket-id="{{ ticket.id }}">
            <i class="bi bi-pencil-square me-2 text-dark"></i>Change Status
        </a>
    </li>
    {% endif %}
    {% if ticket.assigned_to and ticket.assigned_to == session.user_id %}
    <li>
        <a class="dropdown-item modal-trigger" href="#" data-action="drop" data-ticket-id="{{ ticket.id }}">
            <i class="bi bi-person-dash-fill me-2"></i>Drop Ticket
        </a>
    </li>
    {% endif %}
{% endif %} 